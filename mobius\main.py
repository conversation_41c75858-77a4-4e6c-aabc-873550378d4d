"""
Enhanced Mobius AI Assistant - Smarter, Faster, Better
Optimized for concise, intelligent responses with streaming TTS
"""
import os
import sys
import asyncio
import logging
import torch
import json
from pathlib import Path
from typing import Optional, Dict, Any, List
import colorama
from colorama import Fore, Style
from dotenv import load_dotenv
from threading import Thread
from queue import Queue
import time
from datetime import datetime

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.WARNING,  # Less verbose logging
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize colorama
colorama.init()

class SmartLLMEngine:
    """Optimized LLM Engine with personality and better prompting"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model_name = "meta-llama/Meta-Llama-3.1-8B-Instruct"
        self.personality_config = None
        
    def load_model(self):
        """Load Llama 3.1 8B with optimizations"""
        try:
            from transformers import (
                AutoModelForCausalLM, 
                AutoTokenizer, 
                BitsAndBytesConfig,
                TextIteratorStreamer
            )
            
            print(f"{Fore.CYAN}Loading Llama 3.1 8B...{Style.RESET_ALL}")
            
            # 4-bit quantization for RTX 4070
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_quant_type="nf4",
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True
            )
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                token=os.getenv("HF_TOKEN"),
                use_fast=True
            )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                quantization_config=quantization_config,
                device_map="auto",
                torch_dtype=torch.float16,
                token=os.getenv("HF_TOKEN"),
                use_cache=True,
                attn_implementation="eager"
            )
            
            print(f"{Fore.GREEN}✅ Model loaded{Style.RESET_ALL}")
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Failed to load model: {str(e)}{Style.RESET_ALL}")
            return False
    
    def set_personality(self, config: Dict):
        """Set personality configuration"""
        self.personality_config = config
    
    def format_prompt(self, user_input: str, context: Dict) -> str:
        """Format prompt with smart context management"""
        from config import PERSONALITY_CONFIG
        
        # Build system message based on personality
        personality = PERSONALITY_CONFIG.get("current_personality", "concise")
        traits = PERSONALITY_CONFIG["personalities"][personality]
        
        system_message = f"""You are {traits['name']}, {traits['description']}

CRITICAL INSTRUCTIONS:
{traits['instructions']}

Response style: {traits['response_style']}
Max response length: {traits['max_response_length']} words unless specifically asked for more."""

        # Add relevant context (smart selection)
        context_parts = []
        
        # Add user info if available
        if context.get("user_info"):
            context_parts.append(f"User information: {context['user_info']}")
        
        # Add only relevant recent memory
        if context.get("relevant_memory"):
            context_parts.append(f"Relevant context: {context['relevant_memory']}")
        
        # Add current task context
        if context.get("current_task"):
            context_parts.append(f"Current task: {context['current_task']}")
        
        if context_parts:
            system_message += "\n\n" + "\n".join(context_parts)
        
        # Llama 3.1 format
        prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

{system_message}<|eot_id|><|start_header_id|>user<|end_header_id|>

{user_input}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        return prompt
    
    def generate_response_stream(self, prompt: str, max_new_tokens: int = 512):
        """Generate response with streaming"""
        if not self.model or not self.tokenizer:
            yield "Model not loaded."
            return
        
        try:
            from transformers import TextIteratorStreamer
            from threading import Thread
            
            streamer = TextIteratorStreamer(
                self.tokenizer,
                skip_prompt=True,
                skip_special_tokens=True,
                timeout=30.0
            )
            
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=2048
            ).to(self.device)
            
            # Optimized generation parameters
            generation_kwargs = dict(
                **inputs,
                streamer=streamer,
                max_new_tokens=max_new_tokens,
                temperature=0.6,  # Slightly lower for consistency
                top_p=0.85,  # Tighter nucleus
                top_k=30,  # Fewer options for speed
                do_sample=True,
                repetition_penalty=1.15,
                no_repeat_ngram_size=3,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
            )
            
            thread = Thread(target=self.model.generate, kwargs=generation_kwargs)
            thread.start()
            
            generated_text = ""
            for new_text in streamer:
                if new_text:
                    generated_text += new_text
                    yield new_text
                    
                    # Stop at markers
                    if any(marker in generated_text for marker in 
                           ["<|eot_id|>", "<|end|>", "\nUser:", "\nHuman:"]):
                        break
            
            thread.join(timeout=1.0)
            
        except Exception as e:
            yield f"\nError: {str(e)}"


class SmartMemoryManager:
    """Intelligent memory management with context relevance"""
    
    def __init__(self):
        self.short_term = []  # Last 5 exchanges
        self.user_facts = {}  # Key facts about user
        self.important_info = []  # Explicitly remembered items
        self.conversation_summary = ""  # Running summary
        self.memory_file = Path("mobius_memory.json")
        self.load_memory()
    
    def load_memory(self):
        """Load persistent memory"""
        if self.memory_file.exists():
            try:
                with open(self.memory_file, 'r') as f:
                    data = json.load(f)
                    self.user_facts = data.get("user_facts", {})
                    self.important_info = data.get("important_info", [])
            except:
                pass
    
    def save_memory(self):
        """Save persistent memory"""
        data = {
            "user_facts": self.user_facts,
            "important_info": self.important_info,
            "last_updated": datetime.now().isoformat()
        }
        with open(self.memory_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def add_exchange(self, user_input: str, response: str):
        """Add conversation exchange"""
        self.short_term.append({
            "user": user_input,
            "assistant": response,
            "timestamp": time.time()
        })
        
        # Keep only last 5
        if len(self.short_term) > 5:
            self.short_term = self.short_term[-5:]
        
        # Extract important info automatically
        self._extract_user_facts(user_input)
    
    def remember_explicitly(self, content: str):
        """Explicitly remember something"""
        # Parse what to remember intelligently
        clean_content = content.strip()
        
        # Check if it's about the user
        if any(phrase in content.lower() for phrase in ["my name", "i am", "i'm", "call me"]):
            # Extract name
            if "name is" in content.lower():
                name = content.split("name is")[-1].strip()
                self.user_facts["name"] = name
                self.save_memory()
                return f"Got it! I'll remember your name is {name}"
            elif "call me" in content.lower():
                name = content.split("call me")[-1].strip()
                self.user_facts["name"] = name
                self.save_memory()
                return f"I'll call you {name} from now on"
        
        # Otherwise add to important info
        self.important_info.append({
            "content": clean_content,
            "timestamp": datetime.now().isoformat()
        })
        
        # Keep only last 20 important items
        if len(self.important_info) > 20:
            self.important_info = self.important_info[-20:]
        
        self.save_memory()
        return f"Remembered: {clean_content[:50]}..."
    
    def _extract_user_facts(self, user_input: str):
        """Extract facts about user from conversation"""
        lower_input = user_input.lower()
        
        # Name extraction
        if "my name is" in lower_input:
            name = user_input.split("my name is")[-1].split()[0].strip(".,!?")
            self.user_facts["name"] = name
            self.save_memory()
        
        # Age extraction
        if "i am" in lower_input and "years old" in lower_input:
            try:
                age = int(''.join(filter(str.isdigit, user_input)))
                if 1 < age < 120:
                    self.user_facts["age"] = age
                    self.save_memory()
            except:
                pass
    
    def get_context(self, current_query: str) -> Dict:
        """Get relevant context for current query"""
        context = {}
        
        # Add user info if we have it
        if self.user_facts:
            if "name" in self.user_facts:
                context["user_info"] = f"User's name: {self.user_facts['name']}"
            
            # Add other facts if relevant
            facts = []
            for key, value in self.user_facts.items():
                if key != "name":
                    facts.append(f"{key}: {value}")
            if facts:
                context["user_info"] = context.get("user_info", "") + ", " + ", ".join(facts)
        
        # Add relevant memory based on query keywords
        relevant_memory = []
        query_words = set(current_query.lower().split())
        
        # Check important info for relevance
        for info in self.important_info[-5:]:  # Last 5 important items
            info_words = set(info["content"].lower().split())
            if query_words & info_words:  # If there's overlap
                relevant_memory.append(info["content"])
        
        # Add recent context if asking about previous conversation
        if any(word in current_query.lower() for word in ["previous", "earlier", "before", "said", "mentioned"]):
            for exchange in self.short_term[-3:]:
                relevant_memory.append(f"Earlier: {exchange['user'][:50]}")
        
        if relevant_memory:
            context["relevant_memory"] = "; ".join(relevant_memory[:3])  # Max 3 items
        
        # Detect current task type
        if "?" in current_query:
            context["current_task"] = "answering_question"
        elif any(word in current_query.lower() for word in ["write", "create", "make", "code"]):
            context["current_task"] = "creating_content"
        elif any(word in current_query.lower() for word in ["explain", "describe", "what is"]):
            context["current_task"] = "explaining_concept"
        
        return context


class WebSearcher:
    """Improved web search functionality"""
    
    def __init__(self):
        self.search_api = os.getenv("SEARCH_API_KEY")  # For future API integration
    
    async def search(self, query: str, num_results: int = 3) -> str:
        """Perform web search and return formatted results"""
        try:
            import aiohttp
            from bs4 import BeautifulSoup
            
            # Use DuckDuckGo HTML interface
            search_url = f"https://html.duckduckgo.com/html/?q={query.replace(' ', '+')}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(search_url, timeout=10) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        
                        results = []
                        for i, result in enumerate(soup.find_all('div', class_='result')[:num_results]):
                            title = result.find('a', class_='result__a')
                            snippet = result.find('a', class_='result__snippet')
                            
                            if title and snippet:
                                results.append({
                                    "title": title.text.strip(),
                                    "snippet": snippet.text.strip(),
                                    "url": title.get('href', '')
                                })
                        
                        if results:
                            formatted = f"Found {len(results)} results:\n\n"
                            for r in results:
                                formatted += f"**{r['title']}**\n{r['snippet'][:150]}...\n\n"
                            return formatted
                        else:
                            return "No results found."
                    else:
                        return f"Search failed (status {response.status})"
                        
        except Exception as e:
            return f"Search error: {str(e)}"


class TTSManager:
    """Text-to-Speech manager with streaming support"""
    
    def __init__(self):
        self.tts_engine = None
        self.voice_queue = Queue()
        self.is_speaking = False
        
    def initialize(self):
        """Initialize TTS engine"""
        try:
            import pyttsx3
            self.tts_engine = pyttsx3.init()
            
            # Configure voice
            voices = self.tts_engine.getProperty('voices')
            if voices:
                self.tts_engine.setProperty('voice', voices[0].id)  # Use first voice
            
            self.tts_engine.setProperty('rate', 180)  # Speed
            self.tts_engine.setProperty('volume', 0.9)  # Volume
            
            return True
        except:
            print(f"{Fore.YELLOW}TTS not available (install pyttsx3){Style.RESET_ALL}")
            return False
    
    def speak_sentence(self, text: str):
        """Speak a complete sentence"""
        if self.tts_engine and text.strip():
            try:
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            except:
                pass
    
    def start_streaming_speech(self, text_generator):
        """Stream speech as text is generated"""
        if not self.tts_engine:
            return
        
        def speech_worker():
            buffer = ""
            sentence_enders = ['.', '!', '?', '\n']
            
            for chunk in text_generator:
                buffer += chunk
                
                # Check for complete sentences
                for ender in sentence_enders:
                    if ender in buffer:
                        parts = buffer.split(ender, 1)
                        if len(parts) == 2:
                            sentence = parts[0] + ender
                            buffer = parts[1]
                            
                            # Speak the sentence
                            if sentence.strip():
                                self.speak_sentence(sentence)
            
            # Speak remaining buffer
            if buffer.strip():
                self.speak_sentence(buffer)
        
        # Run in separate thread
        thread = Thread(target=speech_worker)
        thread.daemon = True
        thread.start()


class MobiusAssistant:
    """Main Mobius Assistant class"""
    
    def __init__(self):
        self.llm = SmartLLMEngine()
        self.memory = SmartMemoryManager()
        self.web_searcher = WebSearcher()
        self.tts = TTSManager()
        self.running = False
        
    async def start(self):
        """Start the assistant"""
        try:
            print(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}🤖 Mobius AI v3.0 - Smart Edition{Style.RESET_ALL}")
            print(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
            
            # Check GPU
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                print(f"{Fore.GREEN}GPU: {gpu_name}{Style.RESET_ALL}")
            
            # Load model
            if not self.llm.load_model():
                return False
            
            # Initialize TTS
            if self.tts.initialize():
                print(f"{Fore.GREEN}✅ Voice enabled{Style.RESET_ALL}")
            
            # Load personality config
            from config import PERSONALITY_CONFIG
            self.llm.set_personality(PERSONALITY_CONFIG)
            
            print(f"{Fore.GREEN}✅ Ready!{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}Commands: help, remember <text>, search <query>, voice on/off, exit{Style.RESET_ALL}")
            print(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}\n")
            
            self.running = True
            await self._interaction_loop()
            
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Failed to start: {str(e)}{Style.RESET_ALL}")
            return False
    
    async def _interaction_loop(self):
        """Main interaction loop"""
        voice_enabled = False
        
        while self.running:
            try:
                # Get user input
                user_input = input(f"{Fore.GREEN}You: {Style.RESET_ALL}").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                command = user_input.lower().strip()
                
                if command in ["exit", "quit", "bye"]:
                    # Greet by name if we know it
                    name = self.memory.user_facts.get("name", "")
                    if name:
                        print(f"{Fore.CYAN}Goodbye, {name}!{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.CYAN}Goodbye!{Style.RESET_ALL}")
                    break
                
                elif command == "help":
                    self._show_help()
                    continue
                
                elif command.startswith("remember "):
                    content = user_input[9:]
                    result = self.memory.remember_explicitly(content)
                    print(f"{Fore.GREEN}✅ {result}{Style.RESET_ALL}")
                    continue
                
                elif command.startswith("search "):
                    query = user_input[7:]
                    print(f"{Fore.BLUE}Searching...{Style.RESET_ALL}")
                    results = await self.web_searcher.search(query)
                    print(f"{Fore.MAGENTA}Mobius: {Style.RESET_ALL}{results}")
                    continue
                
                elif command == "voice on":
                    voice_enabled = True
                    print(f"{Fore.GREEN}✅ Voice enabled{Style.RESET_ALL}")
                    continue
                
                elif command == "voice off":
                    voice_enabled = False
                    print(f"{Fore.YELLOW}Voice disabled{Style.RESET_ALL}")
                    continue
                
                elif command == "clear":
                    os.system('cls' if os.name == 'nt' else 'clear')
                    continue
                
                # Regular conversation
                print(f"{Fore.MAGENTA}Mobius: {Style.RESET_ALL}", end="", flush=True)
                
                # Get context
                context = self.memory.get_context(user_input)
                
                # Format prompt
                prompt = self.llm.format_prompt(user_input, context)
                
                # Generate and stream response
                full_response = ""
                response_generator = self.llm.generate_response_stream(prompt)
                
                # Start TTS if enabled
                if voice_enabled:
                    # Create two generators - one for display, one for TTS
                    response_list = list(response_generator)
                    
                    # Display
                    for token in response_list:
                        print(token, end="", flush=True)
                        full_response += token
                    
                    # Speak (in background)
                    self.tts.speak_sentence(full_response)
                else:
                    # Just display
                    for token in response_generator:
                        print(token, end="", flush=True)
                        full_response += token
                
                print("\n")
                
                # Store in memory
                self.memory.add_exchange(user_input, full_response)
                
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}Goodbye!{Style.RESET_ALL}")
                break
            except Exception as e:
                print(f"\n{Fore.RED}Error: {str(e)}{Style.RESET_ALL}")
    
    def _show_help(self):
        """Show help"""
        help_text = f"""
{Fore.CYAN}Commands:{Style.RESET_ALL}
  help              - Show this help
  remember <text>   - Remember something
  search <query>    - Web search
  voice on/off      - Toggle voice
  clear             - Clear screen
  exit              - Exit

{Fore.CYAN}Tips:{Style.RESET_ALL}
  • Be direct and specific
  • I remember your name and preferences
  • I give concise answers by default
"""
        print(help_text)


async def main():
    """Main entry point"""
    assistant = MobiusAssistant()
    await assistant.start()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Goodbye!{Style.RESET_ALL}")