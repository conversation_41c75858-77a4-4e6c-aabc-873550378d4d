#!/usr/bin/env python3
"""
Simple test script for Mobius AI Assistant
Tests core functionality without heavy model loading
"""
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()
# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that core modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
        
        import requests
        print(f"✅ Requests available")
        
        from bs4 import BeautifulSoup
        print(f"✅ BeautifulSoup available")
        
        import colorama
        print(f"✅ Colorama available")
        
        # Test our main module
        import mobius.main
        print(f"✅ Mobius main module imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_cuda():
    """Test CUDA availability"""
    print("\n🎮 Testing CUDA...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.get_device_name(0)}")
            print(f"   CUDA version: {torch.version.cuda}")
            print(f"   GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            return True
        else:
            print("⚠️  CUDA not available - will use CPU")
            return True
            
    except Exception as e:
        print(f"❌ CUDA test failed: {e}")
        return False

def test_components():
    """Test individual components"""
    print("\n🔧 Testing components...")
    
    try:
        from mobius.main import SimpleLLMEngine, SimpleMemory, SimpleWebSearch
        
        # Test memory
        memory = SimpleMemory()
        memory.add_conversation("Hello", "Hi there!")
        context = memory.get_context()
        if "Hello" in context:
            print("✅ Memory system working")
        else:
            print("❌ Memory system failed")
            return False
        
        # Test web search (without actually making requests)
        web_search = SimpleWebSearch()
        print("✅ Web search component initialized")
        
        # Test LLM engine (without loading model)
        llm_engine = SimpleLLMEngine()
        print("✅ LLM engine component initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Component test failed: {e}")
        return False

def test_file_operations():
    """Test file operations"""
    print("\n📁 Testing file operations...")
    
    try:
        from mobius.main import MobiusInterface
        
        interface = MobiusInterface()
        
        # Test with README.md if it exists
        if Path("README.md").exists():
            result = interface._handle_file_request("read file README.md")
            if "Content of README.md" in result:
                print("✅ File reading working")
            else:
                print("❌ File reading failed")
                return False
        else:
            print("✅ File operations component ready (no test file)")
        
        return True
        
    except Exception as e:
        print(f"❌ File operations test failed: {e}")
        return False

def test_environment():
    """Test environment setup"""
    print("\n⚙️ Testing environment...")
    
    # Check HF token
    hf_token = os.getenv("HF_TOKEN")
    if hf_token:
        print("✅ HF_TOKEN environment variable set")
    else:
        print("⚠️  HF_TOKEN not set - model loading will fail")
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python version: {python_version.major}.{python_version.minor}")
    else:
        print(f"❌ Python version too old: {python_version.major}.{python_version.minor}")
        return False
    
    return True

def main():
    """Run all tests"""
    print("🚀 Mobius AI Assistant - Simple Test Suite\n")
    
    tests = [
        ("Environment", test_environment),
        ("Imports", test_imports),
        ("CUDA", test_cuda),
        ("Components", test_components),
        ("File Operations", test_file_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
    
    print(f"\n{'='*50}")
    print(f"🧪 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Mobius is ready.")
        print("\nNext steps:")
        print("1. Set HF_TOKEN environment variable if not set")
        print("2. Run: python mobius/main.py")
        return True
    else:
        print("⚠️  Some tests failed. Check output above.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test runner crashed: {e}")
        sys.exit(1)
