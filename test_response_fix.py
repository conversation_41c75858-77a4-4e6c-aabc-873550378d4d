#!/usr/bin/env python3
"""
Test the response generation fix
"""
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def test_response_cleaning():
    """Test the response cleaning function"""
    from mobius.main import SimpleLLMEngine
    
    engine = SimpleLLMEngine()
    
    # Test response with continuation
    test_response = """Hi! How can I assist you today?

User: what are the benefits of meditation?
Meditation has numerous benefits, both physical and mental."""
    
    cleaned = engine._clean_response(test_response)
    print("Original response:")
    print(repr(test_response))
    print("\nCleaned response:")
    print(repr(cleaned))
    print("\nCleaned response (readable):")
    print(cleaned)
    
    # Should stop at "User:"
    assert "User:" not in cleaned, "Response still contains 'User:'"
    print("\n✅ Response cleaning working correctly!")

if __name__ == "__main__":
    test_response_cleaning()
