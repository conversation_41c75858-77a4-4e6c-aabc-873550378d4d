#!/usr/bin/env python3
"""
Test web search functionality
"""
import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

async def test_web_search():
    """Test web search functionality"""
    try:
        from mobius.main import SimpleWebSearch
        
        print("🔍 Testing web search...")
        
        web_search = SimpleWebSearch()
        result = await web_search.search("Python programming")
        
        print(f"Search result length: {len(result)} characters")
        
        if "Search results for" in result:
            print("✅ Web search working!")
            print("\nSample result:")
            print(result[:500] + "..." if len(result) > 500 else result)
        else:
            print("❌ Web search failed")
            print(f"Result: {result}")
        
    except Exception as e:
        print(f"❌ Web search test failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_web_search())
